import 'package:intl/intl.dart';

class AppDateFormatter {
  AppDateFormatter._();

  static String format(DateTime date) {
    return '${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}/${date.year}';
  }

  static String formatJoiningDate(DateTime date) {
    return DateFormat('MM/dd/yyyy').format(date);
  }

  static String formatDateMMddyyyy(DateTime date) {
    return DateFormat('MM-dd-yyyy').format(date);
  }

  static DateTime? parseStringToDateMMddyyyy(String dateString) {
    try {
      return DateFormat('MM-dd-yyyy').parse(dateString);
    } catch (e) {
      return null;
    }
  }

  //DateFormat('MMMM yyyy').
  static String formatCalendarHeader(DateTime date) {
    return DateFormat('MMMM yyyy').format(date);
  }
}
