import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../domain/models/broker_api.dart';
import '../../../domain/repository/broker_repository.dart';

part 'broker_state.dart';

class BrokerCubit extends Cubit<BrokerState> {
  final BrokerRepository _brokerRepository;
  BrokerCubit(this._brokerRepository) : super(BrokerInitial());

  Future<void> fetchBrokers(Map<String, dynamic> payload) async {
    emit(BrokerLoading());
    try {
     
      final brokers = await _brokerRepository.getBrokers(payload);
      emit(BrokerLoaded(brokerApi: brokers));
    } catch (e) {
      emit(BrokerError());
    }
  }
}
