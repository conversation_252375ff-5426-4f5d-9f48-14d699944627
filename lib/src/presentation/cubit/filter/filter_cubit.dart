import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../core/services/exceptions.dart';
import '../../../domain/models/filter/table_filter.dart';
import '../../../domain/repository/filter_repository.dart';

part 'filter_state.dart';

class FilterCubit extends Cubit<FilterState> {
  final FilterRepository _filterRepository;

  FilterCubit(this._filterRepository) : super(FilterInitial());

  Future<void> getBrokerageFilterOptions() async {
    emit(FilterLoading());
    try {
      final filterOptions = await _filterRepository.getBrokerageFilterOptions();
      emit(FilterLoaded(filterOptions: filterOptions));
    } on ApiException catch (e) {
      emit(FilterError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        FilterError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }


  Future<void> getAgentFilterOptions() async {
    emit(FilterLoading());
    try {
      final filterOptions = await _filterRepository.getAgentFilterOptions();
      emit(FilterLoaded(filterOptions: filterOptions));
    } on ApiException catch (e) {
      emit(FilterError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        FilterError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }
}
