part of 'filter_cubit.dart';

@immutable
sealed class FilterState {}

final class FilterInitial extends FilterState {}

final class FilterLoading extends FilterState {}

final class FilterLoaded extends FilterState {
  final List<TableFilter> filterOptions;
  FilterLoaded({required this.filterOptions});
}

final class FilterError extends FilterState {
  final String message;
  final int? statusCode;
  FilterError({required this.message, this.statusCode});
}
