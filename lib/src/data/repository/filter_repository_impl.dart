import 'package:dio/dio.dart';
import '/src/domain/models/filter/table_filter.dart';

import '../../core/config/app_strings.dart';
import '../../core/network/api_config.dart';
import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '../../core/services/exceptions.dart';
import '../../domain/repository/filter_repository.dart';

class FilterRepositoryImpl extends FilterRepository {
  FilterRepositoryImpl();

  static const String brokerFilterOptionsUrl = APIConfig.brokerageFilterOptions;
  static const String agentFilterOptionsUrl = APIConfig.agentFilterOptions;

  @override
  Future<List<TableFilter>> getBrokerageFilterOptions() async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(brokerFilterOptionsUrl);

      if (response.statusCode == 200) {
        final List<dynamic> filterOptionsJson = response.data ?? [];
        final filterOptions = filterOptionsJson
            .map((e) => TableFilter.fromJson(e))
            .toList();
        return filterOptions;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, errorFetchingFilterOptions);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future<List<TableFilter>> getAgentFilterOptions() async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(agentFilterOptionsUrl);

      if (response.statusCode == 200) {
        final List<dynamic> filterOptionsJson = response.data ?? [];
        final filterOptions = filterOptionsJson
            .map((e) => TableFilter.fromJson(e))
            .toList();
        return filterOptions;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, errorFetchingFilterOptions);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }
}
