import '../../core/utils/helper.dart';

class AgentModel {
  final String id;
  final String firstName;
  final String lastName;
  final String fullName;
  final String email;
  final String phone;
  final String state;
  final String city;
  final String level;
  final String companyName;
  final DateTime createdAt;
  final DateTime joiningDate;
  final int totalDownlineAgents;
  final int totalRevenue;
  final int totalDownlineSales;
  final int totalSales;
  final double commission;
  final String referredBy;
  final String associatedBroker;
  final bool status;

  AgentModel({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.fullName,
    required this.email,
    required this.phone,
    required this.state,
    required this.city,
    required this.companyName,
    required this.createdAt,
    required this.joiningDate,
    required this.totalDownlineAgents,
    required this.totalRevenue,
    required this.totalDownlineSales,
    required this.commission,
    required this.associatedBroker,
    required this.level,
    required this.status,
    required this.referredBy,
    required this.totalSales,
  });

  factory AgentModel.fromJson(Map<String, dynamic> json) {
    return AgentModel(
      id: json['id']?.toString() ?? '',
      firstName: json['firstName']?.toString() ?? '',
      lastName: json['lastName']?.toString() ?? '',
      fullName: json['fullName']?.toString() ?? '',
      email: json['email']?.toString() ?? '',
      phone: json['phone']?.toString() ?? '',
      state: json['state']?.toString() ?? '',
      city: json['city']?.toString() ?? '',
      createdAt: parseDate(json['createdAt']),
      joiningDate: parseDate(json['joiningDate']),
      totalDownlineAgents: toInt(json['totalDownlineAgents']),
      totalRevenue: toInt(json['totalRecruitedAgents']),
      totalDownlineSales: toInt(json['totalDownlineSales']),
      totalSales: toInt(json['totalSales']),
      commission: toDouble(json['commission']),
      associatedBroker: json['associatedBroker']?.toString() ?? '',
      level: json['level']?.toString() ?? '',
      status: json['status'] as bool? ?? false,
      referredBy: json['referredBy']?.toString() ?? '',
      companyName: json['companyName']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'firstName': firstName,
      'lastName': lastName,
      'fullName': fullName,
      'email': email,
      'phone': phone,
      'state': state,
      'city': city,
      'companyName': companyName,
      'referredBy': referredBy,
      'createdAt': createdAt.toIso8601String(),
      'joiningDate': joiningDate.toIso8601String(),
      'totalDownlineAgents': totalDownlineAgents,
      'totalRevenue': totalRevenue,
      'totalDownlineSales': totalDownlineSales,
      'totalSales': totalSales,
      'commission': commission,
      'associatedBroker': associatedBroker,
      'level': level,
      'status': status,
    };
  }

  @override
  String toString() {
    return 'AgentModel(id: $id, fullName: $fullName, email: $email, level: $level)';
  }
}

class ApiAgentResponse {
  final List<AgentModel> content;
  final Pageable pageable;
  final int totalElements;
  final int totalPages;
  final bool last;
  final int size;
  final int number;
  final Sort sort;
  final int numberOfElements;
  final bool first;
  final bool empty;

  ApiAgentResponse({
    required this.content,
    required this.pageable,
    required this.totalElements,
    required this.totalPages,
    required this.last,
    required this.size,
    required this.number,
    required this.sort,
    required this.numberOfElements,
    required this.first,
    required this.empty,
  });

  factory ApiAgentResponse.fromJson(Map<String, dynamic> json) {
    return ApiAgentResponse(
      content:
          (json['content'] as List<dynamic>?)
              ?.map((item) => AgentModel.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
      pageable: Pageable.fromJson(
        json['pageable'] as Map<String, dynamic>? ?? {},
      ),
      totalElements: toInt(json['totalElements']),
      totalPages: toInt(json['totalPages']),
      last: json['last'] as bool? ?? false,
      size: toInt(json['size']),
      number: toInt(json['number']),
      sort: Sort.fromJson(json['sort'] as Map<String, dynamic>? ?? {}),
      numberOfElements: toInt(json['numberOfElements']),
      first: json['first'] as bool? ?? false,
      empty: json['empty'] as bool? ?? true,
    );
  }
}

class Pageable {
  final int pageNumber;
  final int pageSize;
  final Sort sort;
  final int offset;
  final bool paged;
  final bool unpaged;

  Pageable({
    required this.pageNumber,
    required this.pageSize,
    required this.sort,
    required this.offset,
    required this.paged,
    required this.unpaged,
  });

  factory Pageable.fromJson(Map<String, dynamic> json) {
    return Pageable(
      pageNumber: toInt(json['pageNumber']),
      pageSize: toInt(json['pageSize']),
      sort: Sort.fromJson(json['sort'] as Map<String, dynamic>? ?? {}),
      offset: toInt(json['offset']),
      paged: json['paged'] as bool? ?? false,
      unpaged: json['unpaged'] as bool? ?? false,
    );
  }
}

class Sort {
  final bool sorted;
  final bool unsorted;
  final bool empty;

  Sort({required this.sorted, required this.unsorted, required this.empty});

  factory Sort.fromJson(Map<String, dynamic> json) {
    return Sort(
      sorted: json['sorted'] as bool? ?? false,
      unsorted: json['unsorted'] as bool? ?? false,
      empty: json['empty'] as bool? ?? true,
    );
  }
}
